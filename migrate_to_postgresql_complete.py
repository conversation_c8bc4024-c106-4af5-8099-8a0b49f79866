#!/usr/bin/env python
"""
Complete script to migrate from SQLite to PostgreSQL
"""
import os
import sys
import django
import json
import subprocess
from pathlib import Path

# Setup Django
project_dir = Path(__file__).resolve().parent
sys.path.append(str(project_dir))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OriginCertificateSystem.settings')

def backup_sqlite_data():
    """Backup SQLite data"""
    print("🔄 Creating backup of SQLite data...")
    
    # Temporarily use SQLite
    os.environ['DATABASE_URL'] = f'sqlite:///{project_dir}/db.sqlite3'
    django.setup()
    
    try:
        from django.core.management import call_command
        from io import StringIO
        
        output = StringIO()
        call_command('dumpdata', 
                    '--natural-foreign', 
                    '--natural-primary',
                    '--exclude=contenttypes',
                    '--exclude=auth.permission',
                    '--exclude=sessions',
                    stdout=output)
        
        backup_file = project_dir / 'sqlite_backup.json'
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(output.getvalue())
        
        print(f"✅ SQLite data backed up to: {backup_file}")
        return backup_file
        
    except Exception as e:
        print(f"❌ Error backing up SQLite data: {e}")
        return None

def switch_to_postgresql():
    """Switch Django settings to PostgreSQL"""
    print("🔄 Switching to PostgreSQL...")
    
    settings_file = project_dir / 'OriginCertificateSystem' / 'settings.py'
    
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Comment out SQLite
        content = content.replace(
            "# SQLite configuration (current - working)",
            "# SQLite configuration (backup)"
        )
        content = content.replace(
            "DATABASES = {\n    'default': {\n        'ENGINE': 'django.db.backends.sqlite3',",
            "# DATABASES = {\n#     'default': {\n#         'ENGINE': 'django.db.backends.sqlite3',"
        )
        content = content.replace(
            "        'NAME': BASE_DIR / 'db.sqlite3',\n    }\n}",
            "#         'NAME': BASE_DIR / 'db.sqlite3',\n#     }\n# }"
        )
        
        # Uncomment PostgreSQL
        content = content.replace(
            "# PostgreSQL configuration (ready for migration)",
            "# PostgreSQL configuration (active)"
        )
        content = content.replace(
            "# DATABASES = {\n#     'default': {\n#         'ENGINE': 'django.db.backends.postgresql',",
            "DATABASES = {\n    'default': {\n        'ENGINE': 'django.db.backends.postgresql',"
        )
        content = content.replace(
            "#         'NAME': 'origin_certificate',\n#         'USER': 'postgres',\n#         'PASSWORD': 'PostgreSQL',  # Correct password\n#         'HOST': 'localhost',\n#         'PORT': '5432',\n#     }\n# }",
            "        'NAME': 'origin_certificate',\n        'USER': 'postgres',\n        'PASSWORD': 'PostgreSQL',\n        'HOST': 'localhost',\n        'PORT': '5432',\n    }\n}"
        )
        
        with open(settings_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Settings switched to PostgreSQL")
        return True
        
    except Exception as e:
        print(f"❌ Error switching settings: {e}")
        return False

def setup_postgresql_database():
    """Setup PostgreSQL database"""
    print("🔄 Setting up PostgreSQL database...")
    
    try:
        # Reload Django with new settings
        django.setup()
        
        from django.core.management import call_command
        
        # Run migrations
        call_command('migrate', verbosity=1)
        
        print("✅ PostgreSQL database setup completed")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up PostgreSQL: {e}")
        return False

def restore_data(backup_file):
    """Restore data to PostgreSQL"""
    if not backup_file or not backup_file.exists():
        print("❌ No backup file found")
        return False
    
    print("🔄 Restoring data to PostgreSQL...")
    
    try:
        from django.core.management import call_command
        
        call_command('loaddata', str(backup_file), verbosity=1)
        
        print("✅ Data restored to PostgreSQL successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error restoring data: {e}")
        return False

def verify_migration():
    """Verify migration success"""
    print("🔄 Verifying migration...")
    
    try:
        from django.contrib.auth.models import User
        from certificate_app.models import Certificate, Branch, Company
        
        users_count = User.objects.count()
        certificates_count = Certificate.objects.count()
        branches_count = Branch.objects.count()
        companies_count = Company.objects.count()
        
        print(f"📊 Migration verification:")
        print(f"  👥 Users: {users_count}")
        print(f"  📜 Certificates: {certificates_count}")
        print(f"  🏢 Branches: {branches_count}")
        print(f"  🏭 Companies: {companies_count}")
        
        if users_count > 0:
            print("✅ Migration verification successful")
            return True
        else:
            print("⚠️ No users found - migration may have failed")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying migration: {e}")
        return False

def main():
    """Main migration function"""
    print("🚀 Starting complete migration to PostgreSQL")
    print("=" * 60)
    
    # Step 1: Backup SQLite data
    backup_file = backup_sqlite_data()
    if not backup_file:
        print("❌ Migration failed: Could not backup SQLite data")
        return False
    
    # Step 2: Switch to PostgreSQL
    if not switch_to_postgresql():
        print("❌ Migration failed: Could not switch settings")
        return False
    
    # Step 3: Setup PostgreSQL
    if not setup_postgresql_database():
        print("❌ Migration failed: Could not setup PostgreSQL")
        return False
    
    # Step 4: Restore data
    if not restore_data(backup_file):
        print("❌ Migration failed: Could not restore data")
        return False
    
    # Step 5: Verify migration
    if not verify_migration():
        print("⚠️ Migration completed but verification failed")
        return False
    
    print("=" * 60)
    print("🎉 Migration to PostgreSQL completed successfully!")
    print("📝 Next steps:")
    print("  1. Test the application thoroughly")
    print("  2. Update any deployment scripts")
    print("  3. Remove SQLite backup when satisfied")
    print("  4. Restart Django server")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

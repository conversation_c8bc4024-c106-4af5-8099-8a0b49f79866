#!/usr/bin/env python
"""
Run Django server with HTTPS support
"""
import os
import sys
import ssl
import socket
from wsgiref import simple_server
from wsgiref.simple_server import make_server, WSGIServer
import django
from django.core.management import execute_from_command_line
from django.core.wsgi import get_wsgi_application

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OriginCertificateSystem.settings')
django.setup()

class HTTPSServer(WSGIServer):
    """HTTPS Server class"""
    
    def __init__(self, server_address, RequestHandlerClass, cert_file, key_file):
        super().__init__(server_address, RequestHandlerClass)
        
        # Create SSL context
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain(cert_file, key_file)
        
        # Wrap socket with SSL
        self.socket = context.wrap_socket(self.socket, server_side=True)
        print(f"🔒 HTTPS Server started with certificate: {cert_file}")

def run_https_server(host='***************', port=8443):
    """Run Django with HTTPS"""
    
    cert_file = 'ssl_certificate.crt'
    key_file = 'ssl_private.key'
    
    # Check if certificate files exist
    if not os.path.exists(cert_file):
        print(f"❌ Certificate file not found: {cert_file}")
        return False
    
    if not os.path.exists(key_file):
        print(f"❌ Private key file not found: {key_file}")
        return False
    
    print("🚀 Starting Django HTTPS Server...")
    print(f"📜 Certificate: {cert_file}")
    print(f"🔑 Private Key: {key_file}")
    print(f"🌐 Address: https://{host}:{port}")
    print("=" * 50)
    
    try:
        # Get Django WSGI application
        application = get_wsgi_application()
        
        # Create HTTPS server
        httpd = make_server(
            host, port, application,
            server_class=lambda addr, handler: HTTPSServer(addr, handler, cert_file, key_file)
        )
        
        print(f"✅ HTTPS Server running on https://{host}:{port}")
        print("🔒 SSL/TLS encryption enabled")
        print("⚠️  If using self-signed certificate, browser will show security warning")
        print("📝 Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Start serving
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error starting HTTPS server: {e}")
        return False

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run Django with HTTPS')
    parser.add_argument('--host', default='***************', help='Host address')
    parser.add_argument('--port', type=int, default=8443, help='Port number')
    parser.add_argument('--cert', default='ssl_certificate.crt', help='Certificate file')
    parser.add_argument('--key', default='ssl_private.key', help='Private key file')
    
    args = parser.parse_args()
    
    # Update file paths if provided
    if args.cert != 'ssl_certificate.crt':
        global cert_file
        cert_file = args.cert
    
    if args.key != 'ssl_private.key':
        global key_file
        key_file = args.key
    
    # Run HTTPS server
    run_https_server(args.host, args.port)

if __name__ == '__main__':
    main()

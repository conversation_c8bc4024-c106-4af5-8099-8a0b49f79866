#!/usr/bin/env python
"""
Setup HTTPS for Django Origin Certificate System
"""
import os
import subprocess
import sys
from pathlib import Path

def check_openssl():
    """Check if OpenSSL is available"""
    try:
        result = subprocess.run(['openssl', 'version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ OpenSSL found: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ OpenSSL not found. Installing...")
        return install_openssl()

def install_openssl():
    """Install OpenSSL using chocolatey or provide instructions"""
    try:
        # Try chocolatey first
        subprocess.run(['choco', 'install', 'openssl', '-y'], 
                      check=True, capture_output=True)
        print("✅ OpenSSL installed via Chocolatey")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ Please install OpenSSL manually:")
        print("1. Download from: https://slproweb.com/products/Win32OpenSSL.html")
        print("2. Or install Chocolatey and run: choco install openssl")
        print("3. Or use Git Bash which includes OpenSSL")
        return False

def extract_pfx_certificate(pfx_path, password=""):
    """Extract certificate and private key from PFX file"""
    pfx_path = Path(pfx_path)
    if not pfx_path.exists():
        print(f"❌ PFX file not found: {pfx_path}")
        return False
    
    cert_path = Path("server.crt")
    key_path = Path("server.key")
    
    try:
        # Extract certificate
        print("🔄 Extracting certificate...")
        cmd_cert = [
            'openssl', 'pkcs12', '-in', str(pfx_path),
            '-clcerts', '-nokeys', '-out', str(cert_path),
            '-passin', f'pass:{password}'
        ]
        subprocess.run(cmd_cert, check=True, capture_output=True)
        
        # Extract private key
        print("🔄 Extracting private key...")
        cmd_key = [
            'openssl', 'pkcs12', '-in', str(pfx_path),
            '-nocerts', '-nodes', '-out', str(key_path),
            '-passin', f'pass:{password}'
        ]
        subprocess.run(cmd_key, check=True, capture_output=True)
        
        print(f"✅ Certificate extracted to: {cert_path}")
        print(f"✅ Private key extracted to: {key_path}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error extracting certificate: {e}")
        # Try with password prompt
        return extract_pfx_with_password(pfx_path)

def extract_pfx_with_password(pfx_path):
    """Extract PFX with password prompt"""
    passwords = ["", "password", "123456", "admin", "acoc", "alexandria"]
    
    for pwd in passwords:
        try:
            print(f"🔄 Trying password: {'(empty)' if not pwd else '***'}")
            
            # Extract certificate
            cmd_cert = [
                'openssl', 'pkcs12', '-in', str(pfx_path),
                '-clcerts', '-nokeys', '-out', 'server.crt',
                '-passin', f'pass:{pwd}'
            ]
            subprocess.run(cmd_cert, check=True, capture_output=True)
            
            # Extract private key
            cmd_key = [
                'openssl', 'pkcs12', '-in', str(pfx_path),
                '-nocerts', '-nodes', '-out', 'server.key',
                '-passin', f'pass:{pwd}'
            ]
            subprocess.run(cmd_key, check=True, capture_output=True)
            
            print(f"✅ Success with password: {'(empty)' if not pwd else '***'}")
            return True
            
        except subprocess.CalledProcessError:
            continue
    
    print("❌ Could not extract certificate with any password")
    return False

def update_django_settings():
    """Update Django settings for HTTPS"""
    settings_file = Path("OriginCertificateSystem/settings.py")
    
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add HTTPS settings
        https_settings = """
# HTTPS Configuration
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

# SSL Certificate paths
SSL_CERTIFICATE = BASE_DIR / 'server.crt'
SSL_PRIVATE_KEY = BASE_DIR / 'server.key'
"""
        
        # Add before the last line
        if "# SSL Certificate paths" not in content:
            content = content.rstrip() + https_settings + "\n"
            
            with open(settings_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Django settings updated for HTTPS")
        else:
            print("✅ HTTPS settings already exist")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating settings: {e}")
        return False

def create_https_server_script():
    """Create script to run Django with HTTPS"""
    script_content = '''#!/usr/bin/env python
"""
Run Django server with HTTPS
"""
import os
import sys
import django
from django.core.management import execute_from_command_line
from django.core.management.commands.runserver import Command as RunServerCommand
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OriginCertificateSystem.settings')
django.setup()

class HTTPSRunServerCommand(RunServerCommand):
    """Custom runserver command with HTTPS support"""
    
    def add_arguments(self, parser):
        super().add_arguments(parser)
        parser.add_argument(
            '--cert', dest='cert_file',
            help='Path to SSL certificate file'
        )
        parser.add_argument(
            '--key', dest='key_file', 
            help='Path to SSL private key file'
        )
    
    def get_handler(self, *args, **options):
        """Return the HTTPS handler"""
        handler = super().get_handler(*args, **options)
        
        cert_file = options.get('cert_file') or 'server.crt'
        key_file = options.get('key_file') or 'server.key'
        
        if os.path.exists(cert_file) and os.path.exists(key_file):
            import ssl
            import socket
            from wsgiref import simple_server
            
            class HTTPSServer(simple_server.WSGIServer):
                def __init__(self, *args, **kwargs):
                    super().__init__(*args, **kwargs)
                    context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                    context.load_cert_chain(cert_file, key_file)
                    self.socket = context.wrap_socket(self.socket, server_side=True)
            
            # Replace the server class
            simple_server.WSGIServer = HTTPSServer
            print(f"🔒 HTTPS enabled with certificate: {cert_file}")
        else:
            print(f"⚠️ Certificate files not found: {cert_file}, {key_file}")
            print("Running in HTTP mode...")
        
        return handler

if __name__ == '__main__':
    # Replace runserver command
    from django.core.management.commands import runserver
    runserver.Command = HTTPSRunServerCommand
    
    # Run Django
    execute_from_command_line(sys.argv)
'''
    
    with open('runserver_https.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ HTTPS server script created: runserver_https.py")

def main():
    """Main setup function"""
    print("🔐 Setting up HTTPS for Django Origin Certificate System")
    print("=" * 60)
    
    # Check OpenSSL
    if not check_openssl():
        print("❌ OpenSSL is required. Please install it first.")
        return False
    
    # Extract certificate from PFX
    pfx_path = Path("server.crt/ACOC_CRT.pfx")
    if not extract_pfx_certificate(pfx_path):
        print("❌ Failed to extract certificate")
        return False
    
    # Update Django settings
    if not update_django_settings():
        print("❌ Failed to update Django settings")
        return False
    
    # Create HTTPS server script
    create_https_server_script()
    
    print("=" * 60)
    print("🎉 HTTPS setup completed!")
    print("📝 Next steps:")
    print("  1. Run: python runserver_https.py ***************:8443")
    print("  2. Access: https://***************:8443")
    print("  3. Accept the self-signed certificate warning")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''

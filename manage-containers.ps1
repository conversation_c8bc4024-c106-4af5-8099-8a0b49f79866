# Container Management Script for Origin Certificate System
# PowerShell script to manage Docker containers on Windows Server 2019

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "status", "logs", "rebuild", "cleanup")]
    [string]$Action
)

$ContainerName = "origin-cert-container"
$ImageName = "origin-cert-app"

Write-Host "=== Origin Certificate System Container Management ===" -ForegroundColor Green
Write-Host "Action: $Action" -ForegroundColor Yellow
Write-Host ""

switch ($Action) {
    "start" {
        Write-Host "Starting container..." -ForegroundColor Cyan
        docker start $ContainerName
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Container started successfully!" -ForegroundColor Green
            Write-Host "Application available at: http://***************:8010" -ForegroundColor Cyan
        } else {
            Write-Host "Failed to start container!" -ForegroundColor Red
        }
    }
    
    "stop" {
        Write-Host "Stopping container..." -ForegroundColor Cyan
        docker stop $ContainerName
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Container stopped successfully!" -ForegroundColor Green
        } else {
            Write-Host "Failed to stop container!" -ForegroundColor Red
        }
    }
    
    "restart" {
        Write-Host "Restarting container..." -ForegroundColor Cyan
        docker restart $ContainerName
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Container restarted successfully!" -ForegroundColor Green
            Write-Host "Application available at: http://***************:8010" -ForegroundColor Cyan
        } else {
            Write-Host "Failed to restart container!" -ForegroundColor Red
        }
    }
    
    "status" {
        Write-Host "Container status:" -ForegroundColor Cyan
        docker ps -a --filter "name=$ContainerName"
        Write-Host ""
        Write-Host "All running containers:" -ForegroundColor Cyan
        docker ps
    }
    
    "logs" {
        Write-Host "Container logs (last 50 lines):" -ForegroundColor Cyan
        docker logs --tail 50 $ContainerName
    }
    
    "rebuild" {
        Write-Host "Rebuilding and restarting container..." -ForegroundColor Cyan
        
        # Stop and remove existing container
        Write-Host "Stopping existing container..." -ForegroundColor Yellow
        docker stop $ContainerName 2>$null
        docker rm $ContainerName 2>$null
        
        # Rebuild image
        Write-Host "Rebuilding Docker image..." -ForegroundColor Yellow
        docker build -t $ImageName .
        
        if ($LASTEXITCODE -eq 0) {
            # Start new container
            Write-Host "Starting new container..." -ForegroundColor Yellow
            docker run -d -p 8010:8000 --name $ContainerName $ImageName
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Container rebuilt and started successfully!" -ForegroundColor Green
                Write-Host "Application available at: http://***************:8010" -ForegroundColor Cyan
            } else {
                Write-Host "Failed to start new container!" -ForegroundColor Red
            }
        } else {
            Write-Host "Failed to rebuild image!" -ForegroundColor Red
        }
    }
    
    "cleanup" {
        Write-Host "Cleaning up Docker resources..." -ForegroundColor Cyan
        
        # Stop and remove container
        Write-Host "Removing container..." -ForegroundColor Yellow
        docker stop $ContainerName 2>$null
        docker rm $ContainerName 2>$null
        
        # Remove image
        Write-Host "Removing image..." -ForegroundColor Yellow
        docker rmi $ImageName 2>$null
        
        # Clean up unused resources
        Write-Host "Cleaning up unused Docker resources..." -ForegroundColor Yellow
        docker system prune -f
        
        Write-Host "Cleanup completed!" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "=== Available Commands ===" -ForegroundColor Yellow
Write-Host ".\manage-containers.ps1 start     - Start the container" -ForegroundColor White
Write-Host ".\manage-containers.ps1 stop      - Stop the container" -ForegroundColor White
Write-Host ".\manage-containers.ps1 restart   - Restart the container" -ForegroundColor White
Write-Host ".\manage-containers.ps1 status    - Show container status" -ForegroundColor White
Write-Host ".\manage-containers.ps1 logs      - Show container logs" -ForegroundColor White
Write-Host ".\manage-containers.ps1 rebuild   - Rebuild and restart" -ForegroundColor White
Write-Host ".\manage-containers.ps1 cleanup   - Remove all resources" -ForegroundColor White

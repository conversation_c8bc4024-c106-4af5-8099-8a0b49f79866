#!/usr/bin/env python
"""
Test PostgreSQL connection and setup database
"""
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def test_postgresql_connection():
    """Test different passwords for PostgreSQL"""
    passwords = [
        'securepassword123', 
        'postgres', 
        '123456', 
        'admin', 
        'password', 
        '1234', 
        'root', 
        ''
    ]
    
    print("🔍 Testing PostgreSQL connection...")
    
    for pwd in passwords:
        try:
            conn = psycopg2.connect(
                host='localhost',
                database='postgres', 
                user='postgres',
                password=pwd,
                port='5432'
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            print(f'✅ SUCCESS: Connected with password: "{pwd if pwd else "(empty)"}"')
            
            # Check if database exists
            cursor.execute('SELECT 1 FROM pg_database WHERE datname=%s', ('origin_certificate',))
            exists = cursor.fetchone()
            
            if exists:
                print('✅ Database origin_certificate already exists')
            else:
                print('📝 Creating origin_certificate database...')
                cursor.execute('CREATE DATABASE origin_certificate')
                print('✅ Database origin_certificate created successfully')
            
            cursor.close()
            conn.close()
            print(f'🎉 PostgreSQL setup completed with password: "{pwd if pwd else "(empty)"}"')
            return pwd
            
        except Exception as e:
            print(f'❌ Failed with password "{pwd if pwd else "(empty)"}": {str(e)[:80]}...')
            continue
    
    print('❌ Could not connect with any password')
    return None

def update_django_settings(password):
    """Update Django settings with correct password"""
    if password is None:
        print("❌ Cannot update settings - no valid password found")
        return False
    
    print(f"📝 Updating Django settings with password: {password}")
    
    try:
        # Read current settings
        with open('OriginCertificateSystem/settings.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace password
        old_line = "'PASSWORD': 'securepassword123',"
        new_line = f"'PASSWORD': '{password}',"
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            # Write back
            with open('OriginCertificateSystem/settings.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Django settings updated successfully")
            return True
        else:
            print("⚠️ Could not find password line in settings.py")
            return False
            
    except Exception as e:
        print(f"❌ Error updating settings: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🚀 PostgreSQL Connection Test")
    print("=" * 50)
    
    # Test connection
    correct_password = test_postgresql_connection()
    
    if correct_password is not None:
        # Update Django settings
        update_django_settings(correct_password)
        
        print("\n" + "=" * 50)
        print("✅ PostgreSQL setup completed successfully!")
        print("📝 Next steps:")
        print("  1. Run: python manage.py migrate")
        print("  2. Run: python manage.py createsuperuser (if needed)")
        print("  3. Run: python manage.py runserver")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("❌ PostgreSQL setup failed!")
        print("📝 Please check:")
        print("  1. PostgreSQL is running")
        print("  2. Correct password")
        print("  3. User 'postgres' exists")
        print("=" * 50)

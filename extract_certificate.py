#!/usr/bin/env python
"""
Extract certificate and private key from PFX file
"""
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.serialization import pkcs12
import os

def extract_pfx_certificate():
    """Extract certificate and private key from PFX file"""

    pfx_path = r"C:\Users\<USER>\Desktop\Origin-Certificate\server.crt\ACOC_CRT.pfx"
    password = "ACOC@1000"

    print("🔄 استخراج الشهادة من ملف PFX...")
    print(f"📁 مسار الملف: {pfx_path}")

    try:
        # قراءة ملف PFX
        with open(pfx_path, 'rb') as f:
            pfx_data = f.read()

        print("✅ تم قراءة ملف PFX بنجاح")

        # استخراج الشهادة والمفتاح
        private_key, certificate, additional_certificates = pkcs12.load_key_and_certificates(
            pfx_data,
            password.encode('utf-8')
        )

        print("✅ تم فك تشفير ملف PFX بنجاح")

        # حفظ الشهادة
        cert_pem = certificate.public_bytes(serialization.Encoding.PEM)
        with open('ssl_certificate.crt', 'wb') as f:
            f.write(cert_pem)
        print("✅ تم حفظ الشهادة في: ssl_certificate.crt")

        # حفظ المفتاح الخاص
        key_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        with open('ssl_private.key', 'wb') as f:
            f.write(key_pem)
        print("✅ تم حفظ المفتاح الخاص في: ssl_private.key")

        # معلومات الشهادة
        print("\n📋 معلومات الشهادة:")
        print(f"  📅 صالحة من: {certificate.not_valid_before}")
        print(f"  📅 صالحة حتى: {certificate.not_valid_after}")
        print(f"  🏢 المُصدر: {certificate.issuer.rfc4514_string()}")
        print(f"  🌐 الموضوع: {certificate.subject.rfc4514_string()}")

        # حفظ الشهادات الإضافية إذا وجدت
        if additional_certificates:
            print(f"📜 شهادات إضافية: {len(additional_certificates)}")
            with open('ca-bundle.crt', 'wb') as f:
                for cert in additional_certificates:
                    f.write(cert.public_bytes(serialization.Encoding.PEM))
            print("✅ تم حفظ الشهادات الإضافية في: ca-bundle.crt")

        print("\n🎉 تم استخراج جميع الملفات بنجاح!")
        return True

    except FileNotFoundError:
        print(f"❌ لم يتم العثور على الملف: {pfx_path}")
        return False
    except Exception as e:
        print(f"❌ خطأ في استخراج الشهادة: {e}")
        return False

def verify_certificate_files():
    """التحقق من صحة الملفات المُستخرجة"""
    print("\n🔍 التحقق من الملفات المُستخرجة...")

    files_to_check = ['ssl_certificate.crt', 'ssl_private.key']
    all_good = True

    for file_name in files_to_check:
        if os.path.exists(file_name):
            size = os.path.getsize(file_name)
            print(f"✅ {file_name}: {size} بايت")
        else:
            print(f"❌ {file_name}: غير موجود")
            all_good = False

    return all_good

if __name__ == "__main__":
    print("🔐 استخراج شهادة SSL لنظام شهادة المصدر")
    print("=" * 50)

    if extract_pfx_certificate():
        if verify_certificate_files():
            print("\n✅ جميع الملفات جاهزة لتشغيل HTTPS!")
        else:
            print("\n❌ هناك مشكلة في الملفات المُستخرجة")
    else:
        print("\n❌ فشل في استخراج الشهادة")

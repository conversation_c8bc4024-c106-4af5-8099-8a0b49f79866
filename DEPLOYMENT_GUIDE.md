# دليل نشر نظام شهادة المصدر على Windows Server 2019

## المتطلبات الأساسية
- Windows Server 2019
- PowerShell (مُثبت افتراضياً)
- اتصال بالإنترنت لتحميل Docker والمكتبات

## الخطوات التفصيلية للنشر

### 1. تحضير المشروع محلياً ✅
تم إعداد المشروع بالملفات التالية:
- `Dockerfile` - محدث ليتطابق مع المتطلبات
- `requirements.txt` - تم إصلاحه وتنظيفه
- `docker-compose.yml` - للإدارة المستقبلية
- `.dockerignore` - لتحسين أداء البناء
- `deploy-to-server.ps1` - سكريبت النشر الآلي

### 2. بناء صورة Docker محلياً (اختياري)
```powershell
docker build -t origin-cert-app .
```

### 3. نقل المشروع إلى السيرفر
انسخ مجلد المشروع كاملاً إلى Windows Server 2019

### 4. تحديث إعدادات Django ✅
تم تحديث `settings.py` ليشمل:
```python
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '***************', '*']
CSRF_TRUSTED_ORIGINS = [
    'http://127.0.0.1:8010',
    'http://localhost:8010',
    'http://***************:8010',  # عنوان IP الخاص بالسيرفر
]
```

### 5. تثبيت Docker على Windows Server 2019

#### الطريقة الأولى: استخدام السكريبت الآلي
```powershell
# تشغيل PowerShell كمدير
.\deploy-to-server.ps1
```

#### الطريقة الثانية: التثبيت اليدوي
```powershell
# تشغيل PowerShell كمدير
Install-Package -Name docker -ProviderName DockerMsftProvider -Force
Restart-Computer

# بعد إعادة التشغيل، تحقق من التثبيت
docker version
docker info
```

### 6. بناء صورة Docker على السيرفر
```powershell
cd C:\path\to\your\project
docker build -t origin-cert-app .
```

### 7. تشغيل الحاوية
```powershell
docker run -d -p 8010:8000 --name origin-cert-container origin-cert-app
```

### 8. الوصول للتطبيق
- محلياً: `http://localhost:8010`
- من الشبكة: `http://***************:8010`

## إدارة الحاويات

### أوامر Docker المفيدة
```powershell
# عرض الحاويات النشطة
docker ps

# عرض سجلات الحاوية
docker logs origin-cert-container

# إيقاف الحاوية
docker stop origin-cert-container

# بدء الحاوية
docker start origin-cert-container

# حذف الحاوية
docker rm origin-cert-container

# حذف الصورة
docker rmi origin-cert-app
```

### استخدام Docker Compose (للمستقبل)
```powershell
# بدء الخدمات
docker-compose up -d

# إيقاف الخدمات
docker-compose down

# إعادة بناء وتشغيل
docker-compose up --build -d
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في بناء الصورة**
   ```powershell
   # تحقق من وجود requirements.txt
   ls requirements.txt
   
   # تحقق من محتوى Dockerfile
   type Dockerfile
   ```

2. **فشل في تشغيل الحاوية**
   ```powershell
   # تحقق من السجلات
   docker logs origin-cert-container
   
   # تحقق من المنافذ المستخدمة
   netstat -an | findstr :8010
   ```

3. **مشاكل الوصول من الشبكة**
   - تحقق من إعدادات Windows Firewall
   - تأكد من أن المنفذ 8010 مفتوح
   - تحقق من عنوان IP الصحيح للسيرفر

## الملفات المُحدثة

### Dockerfile
- تم تحديثه ليتطابق مع الخطوات المرفقة
- استخدام `python -m pip` بدلاً من `pip` مباشرة

### settings.py
- إضافة عنوان IP الخاص بالسيرفر إلى `ALLOWED_HOSTS`
- تحديث `CSRF_TRUSTED_ORIGINS`

### requirements.txt
- تم إصلاح الملف التالف
- تنظيف وترتيب المكتبات

## الخطوات التالية (مستقبلية)

1. **إعداد قاعدة بيانات PostgreSQL**
   - إلغاء التعليق عن خدمة قاعدة البيانات في `docker-compose.yml`
   - تحديث إعدادات قاعدة البيانات في `settings.py`

2. **إعداد HTTPS**
   - إضافة شهادة SSL
   - تحديث إعدادات الأمان

3. **إعداد النسخ الاحتياطي**
   - نسخ احتياطي لقاعدة البيانات
   - نسخ احتياطي للملفات المرفوعة

## معلومات الاتصال والدعم
- عنوان IP السيرفر: ***************
- منفذ التطبيق: 8010
- منفذ الحاوية الداخلي: 8000

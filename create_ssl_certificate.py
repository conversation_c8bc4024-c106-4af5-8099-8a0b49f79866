#!/usr/bin/env python
"""
Create self-signed SSL certificate for Django HTTPS
"""
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa
import datetime
import ipaddress

def create_self_signed_certificate():
    """إنشاء شهادة SSL ذاتية التوقيع"""
    
    print("🔐 إنشاء شهادة SSL ذاتية التوقيع...")
    
    # إنشاء مفتاح خاص
    print("🔑 إنشاء المفتاح الخاص...")
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )
    
    # معلومات الشهادة
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "EG"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Alexandria"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "Alexandria"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Alexandria Chamber of Commerce"),
        x509.NameAttribute(NameOID.ORGANIZATIONAL_UNIT_NAME, "IT Department"),
        x509.NameAttribute(NameOID.COMMON_NAME, "***************"),
    ])
    
    # إنشاء الشهادة
    print("📜 إنشاء الشهادة...")
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.utcnow()
    ).not_valid_after(
        # صالحة لمدة سنة
        datetime.datetime.utcnow() + datetime.timedelta(days=365)
    ).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName("localhost"),
            x509.DNSName("***************"),
            x509.IPAddress(ipaddress.IPv4Address("***************")),
            x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
        ]),
        critical=False,
    ).add_extension(
        x509.BasicConstraints(ca=False, path_length=None),
        critical=True,
    ).add_extension(
        x509.KeyUsage(
            digital_signature=True,
            key_encipherment=True,
            key_agreement=False,
            key_cert_sign=False,
            crl_sign=False,
            content_commitment=False,
            data_encipherment=False,
            encipher_only=False,
            decipher_only=False,
        ),
        critical=True,
    ).add_extension(
        x509.ExtendedKeyUsage([
            x509.oid.ExtendedKeyUsageOID.SERVER_AUTH,
        ]),
        critical=True,
    ).sign(private_key, hashes.SHA256())
    
    # حفظ الشهادة
    print("💾 حفظ الشهادة...")
    with open("server.crt", "wb") as f:
        f.write(cert.public_bytes(serialization.Encoding.PEM))
    
    # حفظ المفتاح الخاص
    print("💾 حفظ المفتاح الخاص...")
    with open("server.key", "wb") as f:
        f.write(private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ))
    
    print("✅ تم إنشاء الشهادة بنجاح!")
    print("📁 الملفات المُنشأة:")
    print("  - server.crt (الشهادة)")
    print("  - server.key (المفتاح الخاص)")
    
    # معلومات الشهادة
    print("\n📋 معلومات الشهادة:")
    print(f"  📅 صالحة من: {cert.not_valid_before}")
    print(f"  📅 صالحة حتى: {cert.not_valid_after}")
    print(f"  🌐 صالحة للعناوين:")
    print("    - localhost")
    print("    - ***************")
    print("    - 127.0.0.1")
    
    return True

if __name__ == "__main__":
    print("🔐 إنشاء شهادة SSL لنظام شهادة المصدر")
    print("=" * 50)
    
    try:
        create_self_signed_certificate()
        print("\n🎉 تم إنشاء الشهادة بنجاح!")
        print("⚠️ ملاحظة: هذه شهادة ذاتية التوقيع للاختبار")
        print("🌐 المتصفح سيعرض تحذير أمان - يمكن تجاهله")
    except Exception as e:
        print(f"\n❌ خطأ في إنشاء الشهادة: {e}")

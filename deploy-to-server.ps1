# PowerShell Script for Deploying Django Project to Windows Server 2019
# Run this script as Administrator

Write-Host "=== Django Origin Certificate System Deployment Script ===" -ForegroundColor Green
Write-Host "Target: Windows Server 2019 with Docker" -ForegroundColor Yellow
Write-Host ""

# Check if Docker is installed
Write-Host "Checking Docker installation..." -ForegroundColor Cyan
try {
    $dockerVersion = docker --version
    Write-Host "Docker found: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "Docker not found! Installing Docker..." -ForegroundColor Red
    Write-Host "Installing Docker for Windows Server 2019..." -ForegroundColor Yellow
    
    # Install Docker
    Install-Package -Name docker -ProviderName DockerMsftProvider -Force
    
    Write-Host "Docker installation completed. Please restart the computer and run this script again." -ForegroundColor Yellow
    Write-Host "After restart, run: docker version && docker info" -ForegroundColor Cyan
    exit
}

# Stop and remove existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Cyan
docker stop origin-cert-container 2>$null
docker rm origin-cert-container 2>$null

# Remove existing image (optional - uncomment if you want to rebuild from scratch)
# docker rmi origin-cert-app 2>$null

# Build the Docker image
Write-Host "Building Docker image..." -ForegroundColor Cyan
docker build -t origin-cert-app .

if ($LASTEXITCODE -eq 0) {
    Write-Host "Docker image built successfully!" -ForegroundColor Green
} else {
    Write-Host "Failed to build Docker image!" -ForegroundColor Red
    exit 1
}

# Run the container
Write-Host "Starting Django application container..." -ForegroundColor Cyan
docker run -d -p 8010:8000 --name origin-cert-container origin-cert-app

if ($LASTEXITCODE -eq 0) {
    Write-Host "Container started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "=== Deployment Completed Successfully! ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "Application is now running on:" -ForegroundColor Yellow
    Write-Host "  Local access: http://localhost:8010" -ForegroundColor Cyan
    Write-Host "  Network access: http://***************:8010" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Useful Docker commands:" -ForegroundColor Yellow
    Write-Host "  View running containers: docker ps" -ForegroundColor White
    Write-Host "  View container logs: docker logs origin-cert-container" -ForegroundColor White
    Write-Host "  Stop container: docker stop origin-cert-container" -ForegroundColor White
    Write-Host "  Start container: docker start origin-cert-container" -ForegroundColor White
    Write-Host "  Remove container: docker rm origin-cert-container" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "Failed to start container!" -ForegroundColor Red
    exit 1
}

# Show container status
Write-Host "Current container status:" -ForegroundColor Cyan
docker ps

# PowerShell Script to Setup PostgreSQL for Origin Certificate System
# Run this script as Administrator

param(
    [switch]$SkipBackup,
    [switch]$ForceReset
)

Write-Host "=== PostgreSQL Setup for Origin Certificate System ===" -ForegroundColor Green
Write-Host ""

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        docker ps > $null 2>&1
        return $true
    } catch {
        return $false
    }
}

# Function to check if container exists
function Test-ContainerExists {
    param($ContainerName)
    $containers = docker ps -a --format "{{.Names}}" 2>$null
    return $containers -contains $ContainerName
}

# Function to check if container is running
function Test-ContainerRunning {
    param($ContainerName)
    $runningContainers = docker ps --format "{{.Names}}" 2>$null
    return $runningContainers -contains $ContainerName
}

# Step 1: Check Docker
Write-Host "🔍 Checking Docker status..." -ForegroundColor Cyan
if (-not (Test-DockerRunning)) {
    Write-Host "❌ Docker is not running or not installed!" -ForegroundColor Red
    Write-Host "Please start Docker Desktop or install Docker first." -ForegroundColor Yellow
    exit 1
}
Write-Host "✅ Docker is running" -ForegroundColor Green

# Step 2: Stop Django server if running
Write-Host "🔍 Checking for running Django server..." -ForegroundColor Cyan
$djangoProcesses = Get-Process python -ErrorAction SilentlyContinue | Where-Object {$_.CommandLine -like "*manage.py*runserver*"}
if ($djangoProcesses) {
    Write-Host "⚠️ Stopping Django server..." -ForegroundColor Yellow
    $djangoProcesses | Stop-Process -Force
    Start-Sleep -Seconds 2
}

# Step 3: Setup PostgreSQL container
Write-Host "🔍 Setting up PostgreSQL container..." -ForegroundColor Cyan

$dbContainerName = "origin-cert-db"

if (Test-ContainerExists $dbContainerName) {
    if ($ForceReset) {
        Write-Host "🗑️ Removing existing PostgreSQL container..." -ForegroundColor Yellow
        docker stop $dbContainerName 2>$null
        docker rm $dbContainerName 2>$null
        docker volume rm origin-certificate_postgres_data 2>$null
    } elseif (Test-ContainerRunning $dbContainerName) {
        Write-Host "✅ PostgreSQL container is already running" -ForegroundColor Green
    } else {
        Write-Host "🔄 Starting existing PostgreSQL container..." -ForegroundColor Yellow
        docker start $dbContainerName
    }
} else {
    Write-Host "🚀 Creating new PostgreSQL container..." -ForegroundColor Yellow
    docker-compose up -d db
}

# Wait for PostgreSQL to be ready
Write-Host "⏳ Waiting for PostgreSQL to be ready..." -ForegroundColor Cyan
$maxAttempts = 30
$attempt = 0

do {
    $attempt++
    Start-Sleep -Seconds 2
    
    try {
        $testResult = docker exec $dbContainerName pg_isready -U postgres 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PostgreSQL is ready!" -ForegroundColor Green
            break
        }
    } catch {
        # Continue waiting
    }
    
    if ($attempt -ge $maxAttempts) {
        Write-Host "❌ PostgreSQL failed to start within timeout" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "  Attempt $attempt/$maxAttempts..." -ForegroundColor Gray
} while ($true)

# Step 4: Test PostgreSQL connection
Write-Host "🔍 Testing PostgreSQL connection..." -ForegroundColor Cyan
try {
    $connectionTest = python -c "
import psycopg2
try:
    conn = psycopg2.connect(host='localhost', database='origin_certificate', user='postgres', password='securepassword123', port='5433')
    print('SUCCESS')
    conn.close()
except Exception as e:
    print(f'ERROR: {e}')
"
    
    if ($connectionTest -eq "SUCCESS") {
        Write-Host "✅ PostgreSQL connection successful" -ForegroundColor Green
    } else {
        Write-Host "❌ PostgreSQL connection failed: $connectionTest" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error testing PostgreSQL connection" -ForegroundColor Red
    exit 1
}

# Step 5: Migrate data
if (-not $SkipBackup) {
    Write-Host "🔄 Starting data migration..." -ForegroundColor Cyan
    try {
        python migrate_to_postgresql.py
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Data migration completed successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Data migration failed" -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "❌ Error during data migration: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "⏭️ Skipping data migration (backup skipped)" -ForegroundColor Yellow
}

# Step 6: Start Django with PostgreSQL
Write-Host "🚀 Starting Django server with PostgreSQL..." -ForegroundColor Cyan
Start-Process -FilePath "python" -ArgumentList "manage.py", "runserver", "***************:8010" -NoNewWindow

# Wait a moment for server to start
Start-Sleep -Seconds 3

# Step 7: Verify everything is working
Write-Host "🔍 Verifying setup..." -ForegroundColor Cyan
$serverCheck = netstat -an | findstr ":8010"
if ($serverCheck) {
    Write-Host "✅ Django server is running on port 8010" -ForegroundColor Green
} else {
    Write-Host "⚠️ Django server may not be running properly" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Setup Complete! ===" -ForegroundColor Green
Write-Host ""
Write-Host "📊 System Status:" -ForegroundColor Yellow
Write-Host "  🐘 PostgreSQL: Running on port 5433" -ForegroundColor White
Write-Host "  🐍 Django: Running on http://***************:8010" -ForegroundColor White
Write-Host "  🗄️ Database: origin_certificate" -ForegroundColor White
Write-Host "  👤 DB User: postgres" -ForegroundColor White
Write-Host "  🔑 DB Password: securepassword123" -ForegroundColor White
Write-Host ""
Write-Host "🌐 Access the application at: http://***************:8010" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Useful commands:" -ForegroundColor Yellow
Write-Host "  docker logs origin-cert-db          # View PostgreSQL logs" -ForegroundColor White
Write-Host "  docker exec -it origin-cert-db psql -U postgres -d origin_certificate  # Access database" -ForegroundColor White
Write-Host "  .\manage-containers.ps1 status      # Check container status" -ForegroundColor White

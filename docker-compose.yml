version: '3.8'

services:
  web:
    build: .
    image: origin-cert-app
    container_name: origin-cert-container
    ports:
      - "8010:8000"
    volumes:
      - .:/app
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - origin-cert-network

networks:
  origin-cert-network:
    driver: bridge

  # PostgreSQL Database Service
  db:
    image: postgres:13
    container_name: origin-cert-db
    environment:
      POSTGRES_DB: origin_certificate
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: securepassword123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"  # Use port 5433 to avoid conflict with existing PostgreSQL
    networks:
      - origin-cert-network
    restart: unless-stopped

volumes:
  postgres_data:

version: '3.8'

services:
  web:
    build: .
    image: origin-cert-app
    container_name: origin-cert-container
    ports:
      - "8010:8000"
    volumes:
      - .:/app
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - origin-cert-network

networks:
  origin-cert-network:
    driver: bridge

# Future database service (PostgreSQL)
# Uncomment when ready to use PostgreSQL
#  db:
#    image: postgres:13
#    container_name: origin-cert-db
#    environment:
#      POSTGRES_DB: origin_certificate
#      POSTGRES_USER: postgres
#      POSTGRES_PASSWORD: your_password_here
#    volumes:
#      - postgres_data:/var/lib/postgresql/data
#    ports:
#      - "5432:5432"
#    networks:
#      - origin-cert-network

#volumes:
#  postgres_data:

#!/usr/bin/env python
"""
<PERSON>ript to migrate data from SQLite to PostgreSQL
"""
import os
import sys
import django
import json
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).resolve().parent
sys.path.append(str(project_dir))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'OriginCertificateSystem.settings')
django.setup()

def backup_sqlite_data():
    """Create a backup of SQLite data"""
    print("🔄 Creating backup of SQLite data...")
    
    # Temporarily switch to SQLite
    from django.conf import settings
    original_db = settings.DATABASES['default'].copy()
    
    # Switch to SQLite
    settings.DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': project_dir / 'db.sqlite3',
    }
    
    # Close existing connections
    from django.db import connections
    connections.close_all()
    
    try:
        # Export data using Django's dumpdata
        from django.core.management import call_command
        from io import StringIO
        
        output = StringIO()
        call_command('dumpdata', 
                    '--natural-foreign', 
                    '--natural-primary',
                    '--exclude=contenttypes',
                    '--exclude=auth.permission',
                    '--exclude=sessions',
                    stdout=output)
        
        # Save to file
        backup_file = project_dir / 'sqlite_backup.json'
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(output.getvalue())
        
        print(f"✅ SQLite data backed up to: {backup_file}")
        return backup_file
        
    except Exception as e:
        print(f"❌ Error backing up SQLite data: {e}")
        return None
    finally:
        # Restore original database settings
        settings.DATABASES['default'] = original_db
        connections.close_all()

def setup_postgresql():
    """Setup PostgreSQL database"""
    print("🔄 Setting up PostgreSQL database...")
    
    try:
        from django.core.management import call_command
        from django.db import connections
        
        # Close all connections
        connections.close_all()
        
        # Run migrations
        print("  📋 Running migrations...")
        call_command('migrate', verbosity=1)
        
        print("✅ PostgreSQL database setup completed")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up PostgreSQL: {e}")
        return False

def restore_data(backup_file):
    """Restore data to PostgreSQL"""
    if not backup_file or not backup_file.exists():
        print("❌ No backup file found")
        return False
    
    print("🔄 Restoring data to PostgreSQL...")
    
    try:
        from django.core.management import call_command
        
        # Load data
        call_command('loaddata', str(backup_file), verbosity=1)
        
        print("✅ Data restored to PostgreSQL successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error restoring data: {e}")
        return False

def verify_migration():
    """Verify the migration was successful"""
    print("🔄 Verifying migration...")
    
    try:
        from django.contrib.auth.models import User
        from certificate_app.models import Certificate, Branch, Company, Country, Cargo
        
        # Count records
        users_count = User.objects.count()
        certificates_count = Certificate.objects.count()
        branches_count = Branch.objects.count()
        companies_count = Company.objects.count()
        countries_count = Country.objects.count()
        cargo_count = Cargo.objects.count()
        
        print(f"📊 Migration verification:")
        print(f"  👥 Users: {users_count}")
        print(f"  📜 Certificates: {certificates_count}")
        print(f"  🏢 Branches: {branches_count}")
        print(f"  🏭 Companies: {companies_count}")
        print(f"  🌍 Countries: {countries_count}")
        print(f"  📦 Cargo: {cargo_count}")
        
        if users_count > 0:
            print("✅ Migration verification successful")
            return True
        else:
            print("⚠️ No users found - migration may have failed")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying migration: {e}")
        return False

def main():
    """Main migration function"""
    print("🚀 Starting migration from SQLite to PostgreSQL")
    print("=" * 50)
    
    # Step 1: Backup SQLite data
    backup_file = backup_sqlite_data()
    if not backup_file:
        print("❌ Migration failed: Could not backup SQLite data")
        return False
    
    # Step 2: Setup PostgreSQL
    if not setup_postgresql():
        print("❌ Migration failed: Could not setup PostgreSQL")
        return False
    
    # Step 3: Restore data
    if not restore_data(backup_file):
        print("❌ Migration failed: Could not restore data")
        return False
    
    # Step 4: Verify migration
    if not verify_migration():
        print("⚠️ Migration completed but verification failed")
        return False
    
    print("=" * 50)
    print("🎉 Migration completed successfully!")
    print("📝 Next steps:")
    print("  1. Test the application with PostgreSQL")
    print("  2. Update Docker configuration if needed")
    print("  3. Remove SQLite backup file when satisfied")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
